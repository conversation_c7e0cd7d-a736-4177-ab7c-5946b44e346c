/**
 * ===================================
 * واجهة النظام المحاسبي الرئيسية المحسنة
 * Enhanced Accounting System Main Interface
 * ===================================
 */

/* ===== حاوي النظام المحاسبي الرئيسي ===== */
.accounting-dashboard {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.accounting-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* ===== رأس النظام المحاسبي ===== */
.accounting-header {
    background: var(--accounting-header-gradient);
    color: white;
    padding: 3rem 2rem;
    border-radius: var(--accounting-radius-xl);
    margin-bottom: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: var(--accounting-shadow-xl);
}

.accounting-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: headerGlow 4s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

.accounting-header h1 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.accounting-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
    position: relative;
    z-index: 2;
}

.accounting-header .header-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
    position: relative;
    z-index: 2;
    animation: float 3s ease-in-out infinite;
}

/* ===== شبكة الوحدات المحاسبية ===== */
.accounting-modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.accounting-module-card {
    background: white;
    border-radius: var(--accounting-radius-xl);
    padding: 2.5rem;
    text-align: center;
    box-shadow: var(--accounting-shadow-lg);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.accounting-module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--accounting-main-gradient);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.accounting-module-card:hover::before {
    transform: scaleX(1);
}

.accounting-module-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--accounting-shadow-xl);
    border-color: rgba(102, 126, 234, 0.3);
}

.accounting-module-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.accounting-module-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    border-radius: 50%;
    filter: blur(10px);
    opacity: 0.3;
    transform: scale(1.2);
}

.accounting-module-card:hover .accounting-module-icon {
    transform: scale(1.1) rotate(5deg);
}

.accounting-module-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--accounting-text-primary);
}

.accounting-module-description {
    font-size: 1rem;
    color: var(--accounting-text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.accounting-module-features {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: right;
}

.accounting-module-features li {
    padding: 0.5rem 0;
    color: var(--accounting-text-secondary);
    font-size: 0.9rem;
    position: relative;
    padding-right: 1.5rem;
}

.accounting-module-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--accounting-profit);
    font-weight: bold;
}

/* ألوان الوحدات المختلفة */
.module-accounts .accounting-module-icon {
    background: var(--accounts-gradient);
}

.module-journal .accounting-module-icon {
    background: var(--journal-gradient);
}

.module-reports .accounting-module-icon {
    background: var(--reports-gradient);
}

.module-payments .accounting-module-icon {
    background: var(--payments-gradient);
}

.module-settings .accounting-module-icon {
    background: var(--settings-gradient);
}

/* ===== الإحصائيات السريعة ===== */
.accounting-quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.quick-stat-card {
    background: white;
    border-radius: var(--accounting-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--accounting-shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accounting-main-gradient);
}

.quick-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--accounting-shadow-lg);
}

.quick-stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: var(--accounting-main-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.quick-stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--accounting-text-primary);
}

.quick-stat-label {
    font-size: 0.9rem;
    color: var(--accounting-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* ===== الإجراءات السريعة ===== */
.accounting-quick-actions {
    background: white;
    border-radius: var(--accounting-radius-xl);
    padding: 2.5rem;
    box-shadow: var(--accounting-shadow-lg);
    margin-bottom: 3rem;
}

.quick-actions-header {
    text-align: center;
    margin-bottom: 2rem;
}

.quick-actions-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accounting-text-primary);
    margin-bottom: 0.5rem;
}

.quick-actions-header p {
    color: var(--accounting-text-secondary);
    font-size: 1rem;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.quick-action-btn {
    background: white;
    border: 2px solid #ecf0f1;
    border-radius: var(--accounting-radius-lg);
    padding: 2rem 1rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: var(--accounting-text-primary);
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--accounting-main-gradient);
    transition: left 0.3s ease;
    z-index: 1;
}

.quick-action-btn:hover::before {
    left: 0;
}

.quick-action-btn:hover {
    border-color: transparent;
    color: white;
    transform: translateY(-5px);
    box-shadow: var(--accounting-shadow-lg);
}

.quick-action-btn * {
    position: relative;
    z-index: 2;
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.quick-action-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-action-desc {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* ===== التنبيهات والإشعارات ===== */
.accounting-alerts {
    background: white;
    border-radius: var(--accounting-radius-xl);
    padding: 2rem;
    box-shadow: var(--accounting-shadow-lg);
}

.alerts-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: var(--accounting-border-light);
}

.alerts-header h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--accounting-text-primary);
    margin: 0;
}

.alert-item {
    padding: 1rem;
    border-radius: var(--accounting-radius-md);
    margin-bottom: 1rem;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.alert-item:hover {
    transform: translateX(5px);
    box-shadow: var(--accounting-shadow-sm);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    border-left-color: var(--accounting-pending);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    border-left-color: var(--accounting-loss);
}

.alert-info {
    background: rgba(52, 152, 219, 0.1);
    border-left-color: var(--accounting-secondary);
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--accounting-text-primary);
}

.alert-text {
    font-size: 0.9rem;
    color: var(--accounting-text-secondary);
    margin: 0;
}

/* ===== الاستجابة للأجهزة المختلفة ===== */

/* الهواتف الصغيرة (أقل من 576px) */
@media (max-width: 575.98px) {
    .accounting-dashboard {
        padding: 1rem 0;
    }

    .accounting-container {
        padding: 0 0.5rem;
    }

    .accounting-header {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
        border-radius: var(--accounting-radius-lg);
    }

    .accounting-header h1 {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
    }

    .accounting-header p {
        font-size: 1rem;
    }

    .accounting-header .header-icon {
        font-size: 3rem;
        margin-bottom: 0.5rem;
    }

    .accounting-modules-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .accounting-module-card {
        padding: 1.5rem;
    }

    .accounting-module-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .accounting-module-title {
        font-size: 1.3rem;
        margin-bottom: 0.75rem;
    }

    .accounting-module-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .accounting-module-features {
        font-size: 0.8rem;
    }

    .accounting-quick-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .quick-stat-card {
        padding: 1.5rem 1rem;
    }

    .quick-stat-icon {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }

    .quick-stat-number {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
    }

    .quick-stat-label {
        font-size: 0.8rem;
    }

    .accounting-quick-actions {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .quick-actions-header h3 {
        font-size: 1.5rem;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .quick-action-btn {
        padding: 1.25rem 0.5rem;
    }

    .quick-action-icon {
        font-size: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .quick-action-title {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .quick-action-desc {
        font-size: 0.75rem;
    }

    .accounting-alerts {
        padding: 1.5rem;
    }

    .alert-item {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .alert-title {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .alert-text {
        font-size: 0.8rem;
    }
}

/* الهواتف الكبيرة (576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .accounting-header {
        padding: 2rem 1.5rem;
        margin-bottom: 2rem;
    }

    .accounting-header h1 {
        font-size: 2.2rem;
    }

    .accounting-modules-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .accounting-module-card {
        padding: 2rem;
    }

    .accounting-module-icon {
        width: 85px;
        height: 85px;
        font-size: 2.2rem;
    }

    .accounting-quick-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

/* الأجهزة اللوحية (768px - 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .accounting-modules-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .accounting-module-card {
        padding: 2rem;
    }

    .accounting-module-icon {
        width: 90px;
        height: 90px;
        font-size: 2.3rem;
    }

    .accounting-quick-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.25rem;
    }
}

/* أجهزة الكمبيوتر الصغيرة (992px - 1199.98px) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .accounting-modules-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .accounting-module-card {
        padding: 2.25rem;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
}

/* أجهزة الكمبيوتر الكبيرة (1200px فما فوق) */
@media (min-width: 1200px) {
    .accounting-container {
        max-width: 1600px;
        padding: 0 2rem;
    }

    .accounting-modules-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }

    .accounting-module-card {
        padding: 2.5rem;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 1.5rem;
    }
}

/* تحسينات خاصة للأندرويد */
@media screen and (max-width: 768px) {
    /* تحسين اللمس للأندرويد */
    .accounting-module-card,
    .quick-action-btn,
    .quick-stat-card {
        min-height: 48px;
        touch-action: manipulation;
    }

    /* منع التكبير التلقائي */
    input, select, textarea {
        font-size: 16px !important;
    }

    /* تحسين التمرير */
    .accounting-dashboard {
        -webkit-overflow-scrolling: touch;
        overflow-x: hidden;
    }

    /* تحسين الأزرار للمس */
    .quick-action-btn {
        min-height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    /* تحسين النصوص للقراءة */
    .accounting-module-title {
        line-height: 1.3;
    }

    .accounting-module-description {
        line-height: 1.5;
    }
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .accounting-module-icon,
    .quick-stat-icon,
    .quick-action-icon {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* تحسينات للوضع الأفقي على الهواتف */
@media (max-height: 500px) and (orientation: landscape) {
    .accounting-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .accounting-header h1 {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
    }

    .accounting-header .header-icon {
        font-size: 2rem;
        margin-bottom: 0.25rem;
    }

    .accounting-modules-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .accounting-module-card {
        padding: 1rem;
    }

    .accounting-module-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .accounting-quick-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .quick-stat-card {
        padding: 1rem 0.5rem;
    }

    .quick-stat-number {
        font-size: 1.2rem;
    }

    .quick-stat-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
}
