/**
 * ===================================
 * JavaScript للنظام المحاسبي المحسن
 * Enhanced Accounting System JavaScript
 * ===================================
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeAccountingDashboard();
    loadQuickStats();
    loadRecentAlerts();
    setupEventListeners();
    setupScrollProgress();
    setupScrollReveal();
    setupInteractiveEnhancements();
});

/**
 * تهيئة لوحة التحكم المحاسبية
 */
function initializeAccountingDashboard() {
    console.log('تم تحميل النظام المحاسبي بنجاح');
    
    // إضافة تأثيرات التحميل
    addLoadingAnimations();
    
    // تحديث الوقت والتاريخ
    updateDateTime();
    setInterval(updateDateTime, 60000); // تحديث كل دقيقة
    
    // تحميل البيانات الأساسية
    loadAccountingData();
}

/**
 * تحميل الإحصائيات السريعة
 */
function loadQuickStats() {
    // محاكاة تحميل البيانات من الخادم
    const stats = {
        totalRevenue: 125450,
        totalExpenses: 85230,
        netProfit: 40220,
        invoiceCount: 156
    };
    
    // تحديث الإحصائيات مع تأثير العد التصاعدي
    animateNumber('.quick-stat-number', stats.totalRevenue, 0);
    
    setTimeout(() => {
        updateStatCards(stats);
    }, 500);
}

/**
 * تحديث بطاقات الإحصائيات
 */
function updateStatCards(stats) {
    const statCards = document.querySelectorAll('.quick-stat-card');
    
    statCards.forEach((card, index) => {
        const numberElement = card.querySelector('.quick-stat-number');
        let value;
        
        switch(index) {
            case 0: value = stats.totalRevenue; break;
            case 1: value = stats.totalExpenses; break;
            case 2: value = stats.netProfit; break;
            case 3: value = stats.invoiceCount; break;
        }
        
        animateNumber(numberElement, value, 1000);
    });
}

/**
 * تأثير العد التصاعدي للأرقام
 */
function animateNumber(element, targetValue, duration = 1000) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (!element) return;
    
    const startValue = 0;
    const increment = targetValue / (duration / 16);
    let currentValue = startValue;
    
    const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= targetValue) {
            currentValue = targetValue;
            clearInterval(timer);
        }
        
        element.textContent = Math.floor(currentValue).toLocaleString('ar-SA');
    }, 16);
}

/**
 * تحميل التنبيهات الحديثة
 */
function loadRecentAlerts() {
    const alerts = [
        {
            type: 'warning',
            title: 'تنبيه: اقتراب نهاية السنة المالية',
            message: 'تبقى 15 يوماً على انتهاء السنة المالية الحالية. يرجى مراجعة جميع القيود والتقارير.',
            timestamp: new Date()
        },
        {
            type: 'danger',
            title: 'تحذير: قيود غير معتمدة',
            message: 'يوجد 8 قيود محاسبية في انتظار المراجعة والاعتماد.',
            timestamp: new Date()
        },
        {
            type: 'info',
            title: 'معلومة: تحديث أسعار الصرف',
            message: 'تم تحديث أسعار صرف العملات الأجنبية بتاريخ اليوم.',
            timestamp: new Date()
        }
    ];
    
    displayAlerts(alerts);
}

/**
 * عرض التنبيهات
 */
function displayAlerts(alerts) {
    const alertsContainer = document.querySelector('.accounting-alerts');
    const badgeElement = alertsContainer.querySelector('.badge');
    
    if (badgeElement) {
        badgeElement.textContent = alerts.length;
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // تأثيرات التمرير على البطاقات
    setupCardHoverEffects();
    
    // تأثيرات النقر على الأزرار
    setupButtonClickEffects();
    
    // تأثيرات التمرير على الصفحة
    setupScrollEffects();
}

/**
 * تأثيرات التمرير على البطاقات
 */
function setupCardHoverEffects() {
    const moduleCards = document.querySelectorAll('.accounting-module-card');
    
    moduleCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

/**
 * تأثيرات النقر على الأزرار
 */
function setupButtonClickEffects() {
    const actionButtons = document.querySelectorAll('.quick-action-btn');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // إضافة تأثير الموجة
            createRippleEffect(e, this);
        });
    });
}

/**
 * إنشاء تأثير الموجة عند النقر
 */
function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 1000;
    `;
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * تأثيرات التمرير على الصفحة
 */
function setupScrollEffects() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // مراقبة البطاقات للتأثيرات
    const cards = document.querySelectorAll('.accounting-module-card, .quick-stat-card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
}

/**
 * إضافة تأثيرات التحميل
 */
function addLoadingAnimations() {
    // إضافة CSS للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(2);
                opacity: 0;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.6s ease forwards;
        }
    `;
    document.head.appendChild(style);
}

/**
 * تحديث الوقت والتاريخ
 */
function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    const dateTimeString = now.toLocaleDateString('ar-SA', options);
    
    // تحديث عنصر التاريخ إذا كان موجوداً
    const dateTimeElement = document.querySelector('.current-datetime');
    if (dateTimeElement) {
        dateTimeElement.textContent = dateTimeString;
    }
}

/**
 * تحميل البيانات المحاسبية
 */
function loadAccountingData() {
    // محاكاة تحميل البيانات
    console.log('جاري تحميل البيانات المحاسبية...');
    
    // يمكن إضافة استدعاءات API هنا
    setTimeout(() => {
        console.log('تم تحميل البيانات المحاسبية بنجاح');
    }, 1000);
}

// ===== وظائف الوحدات المحاسبية =====

/**
 * فتح وحدة دليل الحسابات
 */
function openAccountsModule() {
    console.log('فتح وحدة دليل الحسابات');
    showNotification('جاري تحميل دليل الحسابات...', 'info');
    // يمكن إضافة التنقل إلى صفحة دليل الحسابات
}

/**
 * فتح وحدة القيود اليومية
 */
function openJournalModule() {
    console.log('فتح وحدة القيود اليومية');
    showNotification('جاري تحميل القيود اليومية...', 'info');
    // يمكن إضافة التنقل إلى صفحة القيود اليومية
}

/**
 * فتح وحدة التقارير المالية
 */
function openReportsModule() {
    console.log('فتح وحدة التقارير المالية');
    showNotification('جاري تحميل التقارير المالية...', 'info');
    // يمكن إضافة التنقل إلى صفحة التقارير
}

/**
 * فتح وحدة إدارة المدفوعات
 */
function openPaymentsModule() {
    console.log('فتح وحدة إدارة المدفوعات');
    showNotification('جاري تحميل إدارة المدفوعات...', 'info');
    // يمكن إضافة التنقل إلى صفحة المدفوعات
}

/**
 * فتح وحدة الإعدادات المحاسبية
 */
function openSettingsModule() {
    console.log('فتح وحدة الإعدادات المحاسبية');
    showNotification('جاري تحميل الإعدادات المحاسبية...', 'info');
    // يمكن إضافة التنقل إلى صفحة الإعدادات
}

/**
 * فتح وحدة المخزون المحاسبي
 */
function openInventoryModule() {
    console.log('فتح وحدة المخزون المحاسبي');
    showNotification('جاري تحميل المخزون المحاسبي...', 'info');
    // يمكن إضافة التنقل إلى صفحة المخزون
}

// ===== وظائف الإجراءات السريعة =====

/**
 * إنشاء قيد جديد
 */
function createNewEntry() {
    console.log('إنشاء قيد محاسبي جديد');
    showNotification('جاري فتح نموذج القيد الجديد...', 'success');
}

/**
 * عرض الميزانية العمومية
 */
function viewBalanceSheet() {
    console.log('عرض الميزانية العمومية');
    showNotification('جاري تحميل الميزانية العمومية...', 'info');
}

/**
 * عرض قائمة الدخل
 */
function viewIncomeStatement() {
    console.log('عرض قائمة الدخل');
    showNotification('جاري تحميل قائمة الأرباح والخسائر...', 'info');
}

/**
 * إدارة المدفوعات
 */
function managePayments() {
    console.log('إدارة المدفوعات');
    showNotification('جاري فتح إدارة المدفوعات...', 'info');
}

/**
 * عرض التقارير
 */
function viewReports() {
    console.log('عرض التقارير');
    showNotification('جاري تحميل التقارير المالية...', 'info');
}

/**
 * إنشاء نسخة احتياطية
 */
function backupData() {
    console.log('إنشاء نسخة احتياطية');
    showNotification('جاري إنشاء النسخة الاحتياطية...', 'warning');
}

/**
 * عرض الإشعارات
 */
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification-toast`;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        left: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInLeft 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوانٍ
    setTimeout(() => {
        notification.style.animation = 'slideOutLeft 0.3s ease';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
    
    // إضافة CSS للتأثيرات
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInLeft {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutLeft {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(-100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * إعداد شريط التقدم للتمرير
 */
function setupScrollProgress() {
    const progressBar = document.getElementById('scrollProgress');
    if (!progressBar) return;

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        progressBar.style.width = scrollPercent + '%';
    });
}

/**
 * إعداد تأثيرات الظهور عند التمرير
 */
function setupScrollReveal() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');

                // إضافة تأثير العد التصاعدي للأرقام
                const numberElements = entry.target.querySelectorAll('.counting-animation');
                numberElements.forEach(element => {
                    const targetValue = parseInt(element.textContent.replace(/[^\d]/g, ''));
                    if (targetValue && !element.dataset.animated) {
                        element.dataset.animated = 'true';
                        animateNumber(element, targetValue, 2000);
                    }
                });
            }
        });
    }, observerOptions);

    // مراقبة العناصر
    const revealElements = document.querySelectorAll('.scroll-reveal, .fade-in-up, .fade-in-down, .fade-in-left, .fade-in-right');
    revealElements.forEach(element => {
        element.classList.add('scroll-reveal');
        observer.observe(element);
    });
}

/**
 * إعداد التحسينات التفاعلية
 */
function setupInteractiveEnhancements() {
    // إضافة تأثير الموجة للعناصر القابلة للنقر
    setupRippleEffects();

    // إضافة تأثيرات التمرير المحسنة
    setupAdvancedHoverEffects();

    // إضافة تأثيرات لوحة المفاتيح
    setupKeyboardEffects();

    // إضافة تحسينات الأداء
    setupPerformanceOptimizations();
}

/**
 * إعداد تأثيرات الموجة
 */
function setupRippleEffects() {
    const rippleElements = document.querySelectorAll('.ripple-effect');

    rippleElements.forEach(element => {
        element.addEventListener('click', function(e) {
            createAdvancedRippleEffect(e, this);
        });
    });
}

/**
 * إنشاء تأثير موجة متقدم
 */
function createAdvancedRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.className = 'ripple';
    ripple.style.cssText = `
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
    `;

    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);

    // إزالة التأثير بعد انتهاء الحركة
    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
        }
    }, 600);
}

/**
 * إعداد تأثيرات التمرير المتقدمة
 */
function setupAdvancedHoverEffects() {
    const hoverElements = document.querySelectorAll('.hover-lift, .hover-glow');

    hoverElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.classList.add('will-change-transform');
        });

        element.addEventListener('mouseleave', function() {
            this.classList.remove('will-change-transform');
        });
    });
}

/**
 * إعداد تأثيرات لوحة المفاتيح
 */
function setupKeyboardEffects() {
    document.addEventListener('keydown', function(e) {
        // إضافة تأثيرات خاصة للتنقل بلوحة المفاتيح
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });

    document.addEventListener('mousedown', function() {
        document.body.classList.remove('keyboard-navigation');
    });
}

/**
 * إعداد تحسينات الأداء
 */
function setupPerformanceOptimizations() {
    // إضافة will-change للعناصر المتحركة
    const animatedElements = document.querySelectorAll('.pulse-effect, .hover-lift, .counting-animation');
    animatedElements.forEach(element => {
        element.classList.add('gpu-accelerated');
    });

    // تحسين التمرير
    if ('scrollBehavior' in document.documentElement.style) {
        document.documentElement.style.scrollBehavior = 'smooth';
    }

    // تحسين الخطوط
    if ('fontDisplay' in document.documentElement.style) {
        const fontLinks = document.querySelectorAll('link[href*="fonts.googleapis.com"]');
        fontLinks.forEach(link => {
            link.setAttribute('font-display', 'swap');
        });
    }
}

/**
 * عرض إشعار متقدم
 */
function showAdvancedNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification-toast ${type} fade-in-right`;

    const icon = getNotificationIcon(type);
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="${icon} me-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد المدة المحددة
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease forwards';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

/**
 * الحصول على أيقونة الإشعار
 */
function getNotificationIcon(type) {
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    return icons[type] || icons.info;
}

/**
 * تحديث محسن للإشعارات
 */
function showNotification(message, type = 'info') {
    showAdvancedNotification(message, type);
}
