/**
 * ===================================
 * نظام الألوان والتدرجات المحسن للنظام المحاسبي
 * Enhanced Accounting System Colors & Gradients
 * ===================================
 */

/* ===== متغيرات الألوان المحاسبية ===== */
:root {
    /* ألوان النظام المحاسبي الأساسية */
    --accounting-primary: #2c3e50;
    --accounting-primary-light: #34495e;
    --accounting-primary-dark: #1a252f;
    
    --accounting-secondary: #3498db;
    --accounting-secondary-light: #5dade2;
    --accounting-secondary-dark: #2980b9;
    
    --accounting-accent: #e74c3c;
    --accounting-accent-light: #ec7063;
    --accounting-accent-dark: #c0392b;
    
    /* ألوان الحالة المحاسبية */
    --accounting-profit: #27ae60;
    --accounting-profit-light: #58d68d;
    --accounting-profit-dark: #1e8449;
    
    --accounting-loss: #e74c3c;
    --accounting-loss-light: #ec7063;
    --accounting-loss-dark: #c0392b;
    
    --accounting-neutral: #95a5a6;
    --accounting-neutral-light: #bdc3c7;
    --accounting-neutral-dark: #7f8c8d;
    
    --accounting-pending: #f39c12;
    --accounting-pending-light: #f7dc6f;
    --accounting-pending-dark: #d68910;
    
    /* ألوان الوحدات المحاسبية */
    --accounts-color: #8e44ad;
    --accounts-gradient: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
    
    --journal-color: #2980b9;
    --journal-gradient: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
    
    --reports-color: #27ae60;
    --reports-gradient: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    
    --payments-color: #e67e22;
    --payments-gradient: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
    
    --settings-color: #34495e;
    --settings-gradient: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    
    /* تدرجات خاصة */
    --accounting-main-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --accounting-card-gradient: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    --accounting-header-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    /* ألوان الخلفية */
    --accounting-bg-primary: #ecf0f1;
    --accounting-bg-secondary: #ffffff;
    --accounting-bg-dark: #2c3e50;
    --accounting-bg-light: #f8f9fa;
    
    /* ألوان النص */
    --accounting-text-primary: #2c3e50;
    --accounting-text-secondary: #7f8c8d;
    --accounting-text-light: #bdc3c7;
    --accounting-text-white: #ffffff;
    
    /* الظلال المحاسبية */
    --accounting-shadow-sm: 0 2px 4px rgba(44, 62, 80, 0.1);
    --accounting-shadow-md: 0 4px 8px rgba(44, 62, 80, 0.15);
    --accounting-shadow-lg: 0 8px 16px rgba(44, 62, 80, 0.2);
    --accounting-shadow-xl: 0 12px 24px rgba(44, 62, 80, 0.25);
    
    /* الحدود */
    --accounting-border-light: 1px solid #ecf0f1;
    --accounting-border-medium: 1px solid #bdc3c7;
    --accounting-border-dark: 1px solid #7f8c8d;
    
    /* نصف القطر */
    --accounting-radius-sm: 6px;
    --accounting-radius-md: 10px;
    --accounting-radius-lg: 15px;
    --accounting-radius-xl: 20px;
}

/* ===== فئات الألوان المحاسبية ===== */

/* ألوان الربح والخسارة */
.profit-positive {
    color: var(--accounting-profit) !important;
    font-weight: 600;
}

.profit-negative {
    color: var(--accounting-loss) !important;
    font-weight: 600;
}

.profit-neutral {
    color: var(--accounting-neutral) !important;
    font-weight: 500;
}

/* خلفيات الربح والخسارة */
.bg-profit {
    background: var(--accounting-profit) !important;
    color: white !important;
}

.bg-loss {
    background: var(--accounting-loss) !important;
    color: white !important;
}

.bg-neutral {
    background: var(--accounting-neutral) !important;
    color: white !important;
}

.bg-pending {
    background: var(--accounting-pending) !important;
    color: white !important;
}

/* تدرجات الوحدات */
.bg-accounts {
    background: var(--accounts-gradient) !important;
    color: white !important;
}

.bg-journal {
    background: var(--journal-gradient) !important;
    color: white !important;
}

.bg-reports {
    background: var(--reports-gradient) !important;
    color: white !important;
}

.bg-payments {
    background: var(--payments-gradient) !important;
    color: white !important;
}

.bg-settings {
    background: var(--settings-gradient) !important;
    color: white !important;
}

/* ===== بطاقات محاسبية مخصصة ===== */
.accounting-card {
    background: var(--accounting-card-gradient);
    border: var(--accounting-border-light);
    border-radius: var(--accounting-radius-lg);
    box-shadow: var(--accounting-shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.accounting-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accounting-main-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.accounting-card:hover::before {
    transform: scaleX(1);
}

.accounting-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--accounting-shadow-xl);
}

/* رؤوس البطاقات المحاسبية */
.accounting-card-header {
    background: var(--accounting-header-gradient);
    color: var(--accounting-text-white);
    padding: 1.5rem;
    border-bottom: none;
    font-weight: 600;
    font-size: 1.1rem;
}

.accounting-card-body {
    padding: 2rem;
}

/* ===== أزرار محاسبية مخصصة ===== */
.btn-accounting {
    background: var(--accounting-main-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: var(--accounting-radius-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-accounting::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-accounting:hover::before {
    left: 100%;
}

.btn-accounting:hover {
    transform: translateY(-2px);
    box-shadow: var(--accounting-shadow-lg);
}

/* أزرار الوحدات المحاسبية */
.btn-accounts {
    background: var(--accounts-gradient);
    border: none;
    color: white;
}

.btn-journal {
    background: var(--journal-gradient);
    border: none;
    color: white;
}

.btn-reports {
    background: var(--reports-gradient);
    border: none;
    color: white;
}

.btn-payments {
    background: var(--payments-gradient);
    border: none;
    color: white;
}

.btn-settings {
    background: var(--settings-gradient);
    border: none;
    color: white;
}

/* ===== شارات محاسبية ===== */
.badge-profit {
    background: var(--accounting-profit);
    color: white;
    font-weight: 600;
}

.badge-loss {
    background: var(--accounting-loss);
    color: white;
    font-weight: 600;
}

.badge-pending {
    background: var(--accounting-pending);
    color: white;
    font-weight: 600;
}

.badge-neutral {
    background: var(--accounting-neutral);
    color: white;
    font-weight: 600;
}

/* ===== جداول محاسبية ===== */
.accounting-table {
    background: white;
    border-radius: var(--accounting-radius-lg);
    overflow: hidden;
    box-shadow: var(--accounting-shadow-md);
}

.accounting-table thead th {
    background: var(--accounting-header-gradient);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
    text-align: center;
}

.accounting-table tbody tr {
    transition: all 0.3s ease;
}

.accounting-table tbody tr:hover {
    background: rgba(52, 73, 94, 0.05);
    transform: scale(1.01);
}

.accounting-table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: var(--accounting-border-light);
}

/* ===== نماذج محاسبية ===== */
.accounting-form .form-control,
.accounting-form .form-select {
    border: 2px solid #ecf0f1;
    border-radius: var(--accounting-radius-md);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.accounting-form .form-control:focus,
.accounting-form .form-select:focus {
    border-color: var(--accounting-secondary);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.accounting-form .form-label {
    font-weight: 600;
    color: var(--accounting-text-primary);
    margin-bottom: 0.75rem;
}

/* ===== إحصائيات محاسبية ===== */
.accounting-stat-card {
    background: white;
    border-radius: var(--accounting-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--accounting-shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.accounting-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accounting-main-gradient);
}

.accounting-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--accounting-shadow-xl);
}

.accounting-stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: var(--accounting-main-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.accounting-stat-label {
    font-size: 1rem;
    font-weight: 500;
    color: var(--accounting-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
